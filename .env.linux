# Linux Configuration for Banana Bun
# Copy this to .env and adjust paths as needed

# Base paths - adjust to your preferred location
BASE_PATH=/home/<USER>/banana-data
MEDIA_COLLECTION_PATH=/home/<USER>/media

# OpenAI Configuration
OPENAI_API_KEY=

# Ollama Configuration
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=qwen3:8b
OLLAMA_FAST_MODEL=llama3.2:3b

# ChromaDB Configuration
CHROMA_URL=http://localhost:8000
CHROMA_TENANT=default_tenant

# Meilisearch Configuration
MEILISEARCH_URL=http://localhost:7700
MEILISEARCH_MASTER_KEY=
MEILISEARCH_INDEX_NAME=media_index

# Whisper Configuration
WHISPER_MODEL=turbo
WHISPER_DEVICE=cpu
WHISPER_LANGUAGE=auto
WHISPER_CHUNK_DURATION=30

# Vision/CLIP Configuration
VISION_MODEL=openai/clip-vit-base-patch32
FRAME_INTERVAL_SECONDS=10
MAX_FRAMES_PER_VIDEO=50
ENABLE_SCENE_DETECTION=false

# AWS S3 Configuration (adjust if using S3)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
S3_ENDPOINT=
S3_DEFAULT_BUCKET=
S3_DEFAULT_DOWNLOAD_PATH=/home/<USER>/s3-downloads
S3_SYNC_LOG_PATH=/home/<USER>/banana-data/logs/s3_sync

# Media Collection Paths - adjust to your media directories
MEDIA_COLLECTION_TV=/home/<USER>/media/Shows
MEDIA_COLLECTION_MOVIES=/home/<USER>/media/Movies
MEDIA_COLLECTION_YOUTUBE=/home/<USER>/media/YouTube
MEDIA_COLLECTION_CATCHALL=/home/<USER>/media/Downloads

# Media Tool Paths (should work with system PATH)
FFPROBE_PATH=ffprobe
MEDIAINFO_PATH=mediainfo

# yt-dlp Configuration
YTDLP_PATH=yt-dlp
YTDLP_DEFAULT_FORMAT=best[height<=1080]
YTDLP_DEFAULT_QUALITY=720p
YTDLP_OUTPUT_TEMPLATE=%(title)s [%(id)s].%(ext)s

# RSS Configuration
RSS_ENABLED=true
RSS_CHECK_INTERVAL=3600
RSS_FEEDS=https://example.com/podcast.xml,https://example.com/video-feed.xml
