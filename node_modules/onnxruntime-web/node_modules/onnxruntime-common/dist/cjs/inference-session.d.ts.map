{"version": 3, "file": "inference-session.d.ts", "sourceRoot": "", "sources": ["../../lib/inference-session.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,qBAAqB,EAAE,MAAM,iBAAiB,CAAC;AACnE,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAC1C,OAAO,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AAIpD,MAAM,CAAC,OAAO,WAAW,gBAAgB,CAAC;IAGxC,KAAK,gBAAgB,GAAG;QAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IAC/D,KAAK,wBAAwB,GAAG;QAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,CAAA;KAAE,CAAC;IAE9E;;OAEG;IACH,KAAK,SAAS,GAAG,gBAAgB,CAAC;IAElC;;;;;;;;;;;OAWG;IACH,KAAK,WAAW,GAAG,SAAS,MAAM,EAAE,GAAG,wBAAwB,CAAC;IAEhE;;OAEG;IACH,KAAK,UAAU,GAAG,gBAAgB,CAAC;IAMnC;;OAEG;IACH,UAAiB,cAAe,SAAQ,gBAAgB;QACtD;;;;;WAKG;QACH,kBAAkB,CAAC,EAAE,SAAS,uBAAuB,EAAE,CAAC;QAExD;;;;WAIG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,sBAAsB,CAAC,EAAE;YAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,GAAG,MAAM,CAAA;SAAE,CAAC;QAEtE;;;;WAIG;QACH,sBAAsB,CAAC,EAAE,UAAU,GAAG,OAAO,GAAG,UAAU,GAAG,KAAK,CAAC;QAEnE;;;;WAIG;QACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;QAE5B;;;;WAIG;QACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;QAE3B;;;;WAIG;QACH,aAAa,CAAC,EAAE,YAAY,GAAG,UAAU,CAAC;QAE1C;;;;;WAKG;QACH,sBAAsB,CAAC,EAAE,MAAM,CAAC;QAEhC;;;;WAIG;QACH,eAAe,CAAC,EAAE,OAAO,CAAC;QAE1B;;;;WAIG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf;;;;;WAKG;QACH,gBAAgB,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAErC;;;;WAIG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;;WAKG;QACH,uBAAuB,CAAC,EAAE,qBAAqB,GAAG;YAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,GAAG,qBAAqB,CAAA;SAAE,CAAC;QAE3G;;;WAGG;QACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;QAE7B;;;;;;;;;;;;;;;;;;;WAmBG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KACjC;IASD,UAAU,0BAA0B;QAClC,MAAM,EAAE,6BAA6B,CAAC;QACtC,GAAG,EAAE,0BAA0B,CAAC;QAChC,IAAI,EAAE,2BAA2B,CAAC;QAClC,GAAG,EAAE,0BAA0B,CAAC;QAChC,KAAK,EAAE,4BAA4B,CAAC;QACpC,QAAQ,EAAE,+BAA+B,CAAC;QAC1C,IAAI,EAAE,kCAAkC,CAAC;QACzC,KAAK,EAAE,4BAA4B,CAAC;QACpC,MAAM,EAAE,6BAA6B,CAAC;QACtC,KAAK,EAAE,4BAA4B,CAAC;QACpC,GAAG,EAAE,0BAA0B,CAAC;QAChC,OAAO,EAAE,8BAA8B,CAAC;KACzC;IAED,KAAK,qBAAqB,GAAG,MAAM,0BAA0B,CAAC;IAC9D,KAAK,uBAAuB,GACxB,0BAA0B,CAAC,qBAAqB,CAAC,GACjD,uBAAuB,GACvB,qBAAqB,GACrB,MAAM,CAAC;IAEX,UAAiB,uBAAuB;QACtC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;KACvB;IACD,UAAiB,0BAA2B,SAAQ,uBAAuB;QACzE,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC;QACrB,QAAQ,CAAC,EAAE,OAAO,CAAC;KACpB;IACD,UAAiB,2BAA4B,SAAQ,uBAAuB;QAC1E,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;QACtB,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB;IACD,UAAiB,0BAA2B,SAAQ,uBAAuB;QACzE,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC;QACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB;IACD,UAAiB,+BAAgC,SAAQ,uBAAuB;QAC9E,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC;QAC1B,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB;IACD,UAAiB,kCAAmC,SAAQ,uBAAuB;QACjF,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;KACvB;IACD,UAAiB,4BAA6B,SAAQ,uBAAuB;QAC3E,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;KAExB;IACD,UAAiB,8BAA+B,SAAQ,uBAAuB;QAC7E,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC;KAC1B;IACD,UAAiB,6BAA8B,SAAQ,uBAAuB;QAC5E,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC;QACxB,eAAe,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;KACnC;IAID,UAAU,0BAA2B,SAAQ,uBAAuB;QAClE,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;KACxB;IAED;;;;OAIG;IACH,UAAiB,mBAAmB;QAClC,UAAU,CAAC,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;QACnC,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,eAAe,CAAC,EAAE,SAAS,GAAG,WAAW,GAAG,kBAAkB,CAAC;KAChE;IAED;;OAEG;IACH,UAAiB,4BAA6B,SAAQ,0BAA0B,EAAE,mBAAmB;QACnG,OAAO,CAAC,EAAE,KAAK,CAAC;KACjB;IAED;;;;;;;OAOG;IACH,UAAiB,yBACf,SAAQ,0BAA0B,EAChC,IAAI,CAAC,mBAAmB,EAAE,YAAY,CAAC,EACvC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QACnD,OAAO,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;KACxC;IAED;;;;OAIG;IACH,UAAiB,kBAAmB,SAAQ,0BAA0B;QACpE,OAAO,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACvC,SAAS,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;KAC1C;IAED;;OAEG;IACH,KAAY,4BAA4B,GACpC,4BAA4B,GAC5B,yBAAyB,GACzB,kBAAkB,CAAC;IAIvB,UAAiB,0BAA2B,SAAQ,uBAAuB;QACzE,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC;QACrB;;;;;WAKG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB;;;WAGG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB;;;;WAIG;QACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;KAC/B;IACD,UAAiB,6BAA8B,SAAQ,uBAAuB;QAC5E,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC;QACxB;;;;;;;;;;;;;;;WAeG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB;;;;WAIG;QACH,UAAU,CAAC,EAAE,OAAO,CAAC;QACrB,YAAY,CAAC,EAAE,OAAO,CAAC;QACvB;;;;WAIG;QACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;QAC3B;;;;WAIG;QACH,uBAAuB,CAAC,EAAE,OAAO,CAAC;KACnC;IACD,UAAiB,4BAA6B,SAAQ,uBAAuB;QAC3E,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;QACvB,OAAO,CAAC,EAAE,OAAO,CAAC;QAClB,OAAO,CAAC,EAAE,OAAO,CAAC;QAClB,WAAW,CAAC,EAAE,OAAO,CAAC;QACtB,OAAO,CAAC,EAAE,OAAO,CAAC;KACnB;IAOD;;OAEG;IACH,UAAiB,UAAU;QACzB;;;;;WAKG;QACH,gBAAgB,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAErC;;;;WAIG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,SAAS,CAAC,EAAE,OAAO,CAAC;QAEpB;;;;WAIG;QACH,GAAG,CAAC,EAAE,MAAM,CAAC;QAEb;;;;;;;;;;;;;;;;WAgBG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KACjC;IAMD;;OAEG;IACH,UAAiB,iBAAiB;QAChC;;WAEG;QACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;KACvB;IAED;;OAEG;IACH,UAAiB,sBAAuB,SAAQ,iBAAiB;QAC/D;;WAEG;QACH,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC;KAC1B;IAED;;OAEG;IACH,UAAiB,mBAAoB,SAAQ,iBAAiB;QAC5D;;WAEG;QACH,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC;QACxB;;WAEG;QACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;QAC3B;;;;;;WAMG;QACH,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;KAChD;IAED;;OAEG;IACH,KAAY,aAAa,GAAG,sBAAsB,GAAG,mBAAmB,CAAC;CAG1E;AAED;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAG/B;;;;;;OAMG;IACH,GAAG,CAAC,KAAK,EAAE,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,gBAAgB,CAAC,UAAU,GAAG,OAAO,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAEpH;;;;;;;;OAQG;IACH,GAAG,CACD,KAAK,EAAE,gBAAgB,CAAC,SAAS,EACjC,OAAO,EAAE,gBAAgB,CAAC,WAAW,EACrC,OAAO,CAAC,EAAE,gBAAgB,CAAC,UAAU,GACpC,OAAO,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAMxC;;OAEG;IACH,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAMzB;;OAEG;IACH,cAAc,IAAI,IAAI,CAAC;IAEvB;;OAEG;IACH,YAAY,IAAI,IAAI,CAAC;IAMrB;;OAEG;IACH,QAAQ,CAAC,UAAU,EAAE,SAAS,MAAM,EAAE,CAAC;IAEvC;;OAEG;IACH,QAAQ,CAAC,WAAW,EAAE,SAAS,MAAM,EAAE,CAAC;IAExC;;OAEG;IACH,QAAQ,CAAC,aAAa,EAAE,SAAS,gBAAgB,CAAC,aAAa,EAAE,CAAC;IAElE;;OAEG;IACH,QAAQ,CAAC,cAAc,EAAE,SAAS,gBAAgB,CAAC,aAAa,EAAE,CAAC;CAGpE;AAED,MAAM,WAAW,uBAAuB;IAGtC;;;;;;OAMG;IACH,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,gBAAgB,CAAC,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAE1F;;;;;;OAMG;IACH,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,OAAO,CAAC,EAAE,gBAAgB,CAAC,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAEtG;;;;;;;;OAQG;IACH,MAAM,CACJ,MAAM,EAAE,eAAe,EACvB,UAAU,EAAE,MAAM,EAClB,UAAU,CAAC,EAAE,MAAM,EACnB,OAAO,CAAC,EAAE,gBAAgB,CAAC,cAAc,GACxC,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAE7B;;;;;;OAMG;IACH,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,gBAAgB,CAAC,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;CAGlG;AAGD,eAAO,MAAM,gBAAgB,EAAE,uBAA8C,CAAC"}