"use strict";
// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.InferenceSession = void 0;
const inference_session_impl_js_1 = require("./inference-session-impl.js");
// eslint-disable-next-line @typescript-eslint/naming-convention
exports.InferenceSession = inference_session_impl_js_1.InferenceSession;
//# sourceMappingURL=inference-session.js.map