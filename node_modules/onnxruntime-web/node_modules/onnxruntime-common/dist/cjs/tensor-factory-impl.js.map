{"version": 3, "file": "tensor-factory-impl.js", "sourceRoot": "", "sources": ["../../lib/tensor-factory-impl.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAgBlC,qDAA0C;AAU1C;;;;;;GAMG;AACI,MAAM,cAAc,GAAG,CAAC,MAAqC,EAAE,OAA8B,EAAU,EAAE;IAC9G,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;KACjD;IACD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;QAC/D,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC3D;IACD,IAAI,OAAO,CAAC,YAAY,KAAK,MAAM,EAAE;QACnC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC5D;IAED,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;IAElC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;IACpD,IAAI,QAA0C,CAAC;IAC/C,IAAI,QAA0C,CAAC;IAE/C,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;QACjC,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;KACzD;SAAM;QACL,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;KAChF;IAED,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;QACjC,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;KACzD;SAAM;QACL,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;KAC9E;IAED,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;IAC3E,qEAAqE;IAErE,MAAM,YAAY,GAChB,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACnH,MAAM,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;IAC9B,MAAM,WAAW,GAAG,YAAY,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE1G,8BAA8B;IAC9B,IAAI,IAAI,GAAG,CAAC,EACV,aAAa,GAAG,CAAC,EACjB,aAAa,GAAG,CAAC,EACjB,aAAa,GAAG,CAAC,EACjB,aAAa,GAAG,CAAC,CAAC;IACpB,IAAI,cAAc,GAAG,CAAC,EACpB,cAAc,GAAG,MAAM,EACvB,cAAc,GAAG,MAAM,GAAG,CAAC,EAC3B,cAAc,GAAG,CAAC,CAAC,CAAC;IAEtB,mEAAmE;IACnE,IAAI,WAAW,KAAK,KAAK,EAAE;QACzB,IAAI,GAAG,CAAC,CAAC;QACT,aAAa,GAAG,CAAC,CAAC;QAClB,aAAa,GAAG,CAAC,CAAC;QAClB,aAAa,GAAG,CAAC,CAAC;QAClB,aAAa,GAAG,CAAC,CAAC,CAAC;KACpB;IAED,qEAAqE;IACrE,IAAI,YAAY,KAAK,MAAM,EAAE;QAC3B,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;KAC7B;SAAM,IAAI,YAAY,KAAK,KAAK,EAAE;QACjC,cAAc,GAAG,CAAC,CAAC;QACnB,cAAc,GAAG,MAAM,CAAC;QACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;KAC7B;SAAM,IAAI,YAAY,KAAK,KAAK,EAAE;QACjC,cAAc,GAAG,CAAC,CAAC;QACnB,cAAc,GAAG,MAAM,CAAC;QACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;KAC7B;IAED,KACE,IAAI,CAAC,GAAG,CAAC,EACT,CAAC,GAAG,MAAM,EACV,CAAC,EAAE,EAAE,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAC/F;QACA,WAAW,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACpF,WAAW,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACpF,WAAW,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACpF,IAAI,cAAc,KAAK,CAAC,CAAC,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;YACjD,WAAW,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;SACrF;KACF;IAED,6BAA6B;IAC7B,MAAM,YAAY,GAChB,YAAY,KAAK,MAAM;QACrB,CAAC,CAAC,IAAI,uBAAM,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,IAAI,uBAAM,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;IAChE,OAAO,YAAY,CAAC;AACtB,CAAC,CAAC;AAzFW,QAAA,cAAc,kBAyFzB;AAEF;;GAEG;AACI,MAAM,eAAe,GAAG,KAAK,EAClC,KAA0D,EAC1D,OAIwB,EACP,EAAE;IACnB,oCAAoC;IACpC,MAAM,cAAc,GAAG,OAAO,gBAAgB,KAAK,WAAW,IAAI,KAAK,YAAY,gBAAgB,CAAC;IACpG,MAAM,cAAc,GAAG,OAAO,SAAS,KAAK,WAAW,IAAI,KAAK,YAAY,SAAS,CAAC;IACtF,MAAM,aAAa,GAAG,OAAO,WAAW,KAAK,WAAW,IAAI,KAAK,YAAY,WAAW,CAAC;IACzF,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC;IAE3C,IAAI,IAAmC,CAAC;IACxC,IAAI,qBAAqB,GAA0B,OAAO,IAAI,EAAE,CAAC;IAEjE,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACnC,OAAO,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;SACzC;aAAM,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE;YACjD,OAAO,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAClC;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;IACH,CAAC,CAAC;IACF,MAAM,mBAAmB,GAAG,CAAC,MAA2C,EAAE,EAAE;QAC1E,IAAI,OAAO,iBAAiB,KAAK,WAAW,IAAI,MAAM,YAAY,iBAAiB,EAAE;YACnF,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAChC;aAAM,IAAI,MAAM,YAAY,eAAe,EAAE;YAC5C,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAAsC,CAAC;SACrE;aAAM;YACL,OAAO,IAAI,CAAC;SACb;IACH,CAAC,CAAC;IACF,mDAAmD;IACnD,IAAI,cAAc,EAAE;QAClB,8DAA8D;QAC9D,MAAM,MAAM,GAAG,YAAY,EAAE,CAAC;QAC9B,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAC3B,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC7B,MAAM,eAAe,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAEpD,IAAI,eAAe,IAAI,IAAI,EAAE;YAC3B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC1B,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YACxB,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;gBACtG,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;gBAC/B,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC;aAC9B;YAED,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,qBAAqB,GAAG,OAAO,CAAC;gBAChC,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;oBACtC,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;iBAChF;qBAAM;oBACL,qBAAqB,CAAC,YAAY,GAAG,MAAM,CAAC;iBAC7C;gBACD,qBAAqB,CAAC,MAAM,GAAG,MAAM,CAAC;gBACtC,qBAAqB,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC;iBAAM;gBACL,qBAAqB,CAAC,YAAY,GAAG,MAAM,CAAC;gBAC5C,qBAAqB,CAAC,MAAM,GAAG,MAAM,CAAC;gBACtC,qBAAqB,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC;YAED,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC;SAC/D;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAC9C;KACF;SAAM,IAAI,cAAc,EAAE;QACzB,IAAI,MAAc,CAAC;QACnB,IAAI,KAAa,CAAC;QAElB,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,EAAE;YACtG,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;YAC/B,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC;SAC9B;aAAM;YACL,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACtB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;SACrB;QAED,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,qBAAqB,GAAG,OAAO,CAAC;SACjC;QACD,qBAAqB,CAAC,MAAM,GAAG,MAAM,CAAC;QACtC,qBAAqB,CAAC,MAAM,GAAG,MAAM,CAAC;QACtC,qBAAqB,CAAC,KAAK,GAAG,KAAK,CAAC;QAEpC,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,MAAM,UAAU,GAAG,YAAY,EAAE,CAAC;YAElC,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;YACzB,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;YAE3B,MAAM,eAAe,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAExD,IAAI,eAAe,IAAI,IAAI,EAAE;gBAC3B,eAAe,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC1C,IAAI,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC;aAC/D;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;aAC9C;SACF;aAAM;YACL,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;SACnB;KACF;SAAM,IAAI,aAAa,EAAE;QACxB,+DAA+D;QAC/D,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;SAC5E;QAED,MAAM,MAAM,GAAG,YAAY,EAAE,CAAC;QAC9B,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAC3B,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC7B,MAAM,eAAe,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAEpD,IAAI,eAAe,IAAI,IAAI,EAAE;YAC3B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC5B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YAC1B,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YACtD,IAAI,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC;YAC9D,qBAAqB,CAAC,MAAM,GAAG,MAAM,CAAC;YACtC,qBAAqB,CAAC,KAAK,GAAG,KAAK,CAAC;YACpC,OAAO,IAAA,sBAAc,EAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;SACpD;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAC9C;KACF;SAAM,IAAI,QAAQ,EAAE;QACnB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,YAAY,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC5C,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,EAAE;gBACtB,OAAO,MAAM,EAAE,CAAC;aACjB;YACD,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;YAC7B,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;YACnC,QAAQ,CAAC,GAAG,GAAG,KAAK,CAAC;YACrB,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE;gBACrB,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;gBAC9B,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;gBAChC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC/D,MAAM,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBAEpE,qBAAqB,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;gBAC7C,qBAAqB,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;gBAC3C,OAAO,CAAC,IAAA,sBAAc,EAAC,GAAG,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;KACnF;IAED,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,OAAO,IAAA,sBAAc,EAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;KACpD;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;KACnF;AACH,CAAC,CAAC;AA/JW,QAAA,eAAe,mBA+J1B;AAEF;;GAEG;AACI,MAAM,iBAAiB,GAAG,CAC/B,OAAoC,EACpC,OAAoC,EAC5B,EAAE;IACV,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;IACrD,gEAAgE;IAChE,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IACnC,OAAO,IAAI,uBAAM,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;AAChG,CAAC,CAAC;AARW,QAAA,iBAAiB,qBAQ5B;AAEF;;GAEG;AACI,MAAM,mBAAmB,GAAG,CACjC,SAAwC,EACxC,OAAsC,EAC9B,EAAE;IACV,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;IACtD,OAAO,IAAI,uBAAM,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,IAAI,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;AACjH,CAAC,CAAC;AANW,QAAA,mBAAmB,uBAM9B;AAEF;;GAEG;AACI,MAAM,kBAAkB,GAAG,CAChC,QAAsC,EACtC,OAAqC,EAC7B,EAAE;IACV,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;IACtD,OAAO,IAAI,uBAAM,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,IAAI,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;AAC/G,CAAC,CAAC;AANW,QAAA,kBAAkB,sBAM7B;AAEF;;GAEG;AACI,MAAM,sBAAsB,GAAG,CACpC,IAAO,EACP,MAAsC,EACtC,IAAwB,EAChB,EAAE,CAAC,IAAI,uBAAM,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAJ1F,QAAA,sBAAsB,0BAIoE"}