{"version": 3, "file": "tensor-impl.d.ts", "sourceRoot": "", "sources": ["../../lib/tensor-impl.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,sBAAsB,EAAE,wBAAwB,EAAE,MAAM,wBAAwB,CAAC;AAQ1F,OAAO,EACL,8BAA8B,EAC9B,8BAA8B,EAC9B,6BAA6B,EAC7B,0BAA0B,EAC1B,4BAA4B,EAC5B,0BAA0B,EAC1B,6BAA6B,EAC7B,yBAAyB,EACzB,wBAAwB,EACxB,oBAAoB,EACpB,4BAA4B,EAC7B,MAAM,qBAAqB,CAAC;AAS7B,OAAO,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,aAAa,CAAC;AAIxD,KAAK,UAAU,GAAG,eAAe,CAAC,IAAI,CAAC;AACvC,KAAK,cAAc,GAAG,eAAe,CAAC,QAAQ,CAAC;AAC/C,KAAK,kBAAkB,GAAG,eAAe,CAAC,YAAY,CAAC;AACvD,KAAK,iBAAiB,GAAG,eAAe,CAAC,WAAW,CAAC;AACrD,KAAK,mBAAmB,GAAG,eAAe,CAAC,aAAa,CAAC;AACzD,KAAK,kBAAkB,GAAG,eAAe,CAAC,YAAY,CAAC;AAEvD;;;;GAIG;AACH,qBAAa,MAAO,YAAW,eAAe;IAG5C;;OAEG;gBAED,IAAI,EAAE,UAAU,EAChB,IAAI,EAAE,cAAc,GAAG,iBAAiB,GAAG,SAAS,MAAM,EAAE,GAAG,SAAS,MAAM,EAAE,GAAG,SAAS,OAAO,EAAE,EACrG,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE;IAE1B;;OAEG;gBAED,IAAI,EAAE,cAAc,GAAG,iBAAiB,GAAG,SAAS,MAAM,EAAE,GAAG,SAAS,OAAO,EAAE,EACjF,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE;IAE1B;;;;;;OAMG;gBACS,MAAM,EAAE,8BAA8B;IAClD;;;;;;OAMG;gBACS,MAAM,EAAE,4BAA4B;IAChD;;;;;;OAMG;gBACS,MAAM,EAAE,8BAA8B;IAElD;;;;;;OAMG;gBACS,MAAM,EAAE,6BAA6B;WAqPpC,SAAS,CACpB,KAAK,EAAE,SAAS,GAAG,gBAAgB,GAAG,WAAW,GAAG,MAAM,EAC1D,OAAO,CAAC,EACJ,0BAA0B,GAC1B,6BAA6B,GAC7B,4BAA4B,GAC5B,oBAAoB,GACvB,OAAO,CAAC,eAAe,CAAC;IAI3B,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,eAAe,CAAC,gBAAgB,EAC3D,OAAO,EAAE,iBAAiB,EAC1B,OAAO,EAAE,wBAAwB,CAAC,CAAC,CAAC,GACnC,eAAe;IAIlB,MAAM,CAAC,aAAa,CAAC,CAAC,SAAS,eAAe,CAAC,kBAAkB,EAC/D,SAAS,EAAE,mBAAmB,EAC9B,OAAO,EAAE,0BAA0B,CAAC,CAAC,CAAC,GACrC,eAAe;IAIlB,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,eAAe,CAAC,iBAAiB,EAC7D,QAAQ,EAAE,kBAAkB,EAC5B,OAAO,EAAE,yBAAyB,CAAC,CAAC,CAAC,GACpC,eAAe;IAIlB,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,eAAe,CAAC,kBAAkB,EAClE,IAAI,EAAE,CAAC,EACP,MAAM,EAAE,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,EACtC,IAAI,CAAC,EAAE,SAAS,MAAM,EAAE,GACvB,MAAM;IAOT,SAAS,CAAC,OAAO,CAAC,EAAE,sBAAsB,GAAG,MAAM;IAInD,WAAW,CAAC,OAAO,CAAC,EAAE,wBAAwB,GAAG,SAAS;IAM1D,QAAQ,CAAC,IAAI,EAAE,SAAS,MAAM,EAAE,CAAC;IACjC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC;IAC1B,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IAKtB;;OAEG;IACH,OAAO,CAAC,YAAY,CAAqB;IAEzC;;OAEG;IACH,OAAO,CAAC,OAAO,CAAC,CAAiB;IAEjC;;OAEG;IACH,OAAO,CAAC,cAAc,CAAC,CAAoB;IAE3C;;OAEG;IACH,OAAO,CAAC,aAAa,CAAC,CAAsB;IAE5C;;OAEG;IACH,OAAO,CAAC,YAAY,CAAC,CAAqB;IAE1C;;OAEG;IACH,OAAO,CAAC,UAAU;IAElB;;OAEG;IACH,OAAO,CAAC,aAAa,CAAC,CAAU;IAEhC;;OAEG;IACH,OAAO,CAAC,QAAQ;IAIhB,IAAI,IAAI,IAAI,cAAc,CASzB;IAED,IAAI,QAAQ,IAAI,kBAAkB,CAEjC;IAED,IAAI,OAAO,IAAI,iBAAiB,CAM/B;IAED,IAAI,SAAS,IAAI,mBAAmB,CAMnC;IAED,IAAI,QAAQ,IAAI,kBAAkB,CAMjC;IAKK,OAAO,CAAC,WAAW,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC;IAqC7D,OAAO,IAAI,IAAI;IAsBf,OAAO,CAAC,WAAW;IAMnB,OAAO,CAAC,IAAI,EAAE,SAAS,MAAM,EAAE,GAAG,eAAe;CAQlD"}