{"version": 3, "file": "tensor-impl.js", "sourceRoot": "", "sources": ["../../lib/tensor-impl.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,2EAAiF;AAEjF,qEAMkC;AAclC,+EAMuC;AACvC,iEAAsE;AAYtE;;;;GAIG;AACH,MAAa,MAAM;IAoDjB;;OAEG;IACH,YACE,IASiC,EACjC,IAAsG,EACtG,IAAwB;QAExB,yDAAyD;QACzD,IAAA,6CAAe,GAAE,CAAC;QAElB,IAAI,IAAgB,CAAC;QACrB,IAAI,IAAuB,CAAC;QAE5B,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,UAAU,IAAI,IAAI,EAAE;YAClD,EAAE;YACF,6CAA6C;YAC7C,EAAE;YACF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;YAClC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACjB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACjB,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBACrB,KAAK,YAAY,CAAC,CAAC;oBACjB,MAAM,6BAA6B,GAAG,mEAAqC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBACtF,IAAI,CAAC,6BAA6B,EAAE;wBAClC,MAAM,IAAI,SAAS,CAAC,qBAAqB,IAAI,uCAAuC,CAAC,CAAC;qBACvF;oBACD,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,YAAY,6BAA6B,CAAC,EAAE;wBACzD,MAAM,IAAI,SAAS,CAAC,4BAA4B,6BAA6B,CAAC,IAAI,EAAE,CAAC,CAAC;qBACvF;oBACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;oBACzB,MAAM;iBACP;gBACD,KAAK,SAAS,CAAC,CAAC;oBACd,IAAI,IAAI,KAAK,SAAS,EAAE;wBACtB,MAAM,IAAI,SAAS,CAAC,qBAAqB,IAAI,iCAAiC,CAAC,CAAC;qBACjF;oBACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC;oBACnC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;oBAC7B,MAAM;iBACP;gBACD,KAAK,YAAY,CAAC,CAAC;oBACjB,IACE,IAAI,KAAK,SAAS;wBAClB,IAAI,KAAK,SAAS;wBAClB,IAAI,KAAK,OAAO;wBAChB,IAAI,KAAK,OAAO;wBAChB,IAAI,KAAK,QAAQ;wBACjB,IAAI,KAAK,OAAO;wBAChB,IAAI,KAAK,MAAM;wBACf,IAAI,KAAK,OAAO;wBAChB,IAAI,KAAK,MAAM,EACf;wBACA,MAAM,IAAI,SAAS,CAAC,qBAAqB,IAAI,oCAAoC,CAAC,CAAC;qBACpF;oBACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;oBACpC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;oBAC7B,MAAM;iBACP;gBACD,KAAK,WAAW,CAAC,CAAC;oBAChB,IACE,IAAI,KAAK,SAAS;wBAClB,IAAI,KAAK,SAAS;wBAClB,IAAI,KAAK,OAAO;wBAChB,IAAI,KAAK,OAAO;wBAChB,IAAI,KAAK,QAAQ;wBACjB,IAAI,KAAK,QAAQ;wBACjB,IAAI,KAAK,MAAM;wBACf,IAAI,KAAK,OAAO;wBAChB,IAAI,KAAK,MAAM;wBACf,IAAI,KAAK,OAAO;wBAChB,IAAI,KAAK,MAAM,EACf;wBACA,MAAM,IAAI,SAAS,CAAC,qBAAqB,IAAI,kCAAkC,CAAC,CAAC;qBAClF;oBACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;oBAC7B,MAAM;iBACP;gBACD;oBACE,MAAM,IAAI,KAAK,CAAC,6CAA6C,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;aACtF;SACF;aAAM;YACL,EAAE;YACF,wCAAwC;YACxC,EAAE;YACF,IAAI,IAAoB,CAAC;YACzB,IAAI,SAAoC,CAAC;YACzC,qCAAqC;YACrC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,EAAE;gBACF,yCAAyC;gBACzC,EAAE;gBACF,IAAI,GAAG,IAAI,CAAC;gBACZ,SAAS,GAAG,IAAI,CAAC;gBACjB,IAAI,IAAI,KAAK,QAAQ,EAAE;oBACrB,gBAAgB;oBAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACxB,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;qBACvE;oBACD,4GAA4G;oBAC5G,uCAAuC;oBACvC,IAAI,GAAG,IAAI,CAAC;iBACb;qBAAM;oBACL,iBAAiB;oBACjB,MAAM,qBAAqB,GAAG,mEAAqC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC9E,IAAI,qBAAqB,KAAK,SAAS,EAAE;wBACvC,MAAM,IAAI,SAAS,CAAC,4BAA4B,IAAI,GAAG,CAAC,CAAC;qBAC1D;oBACD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACvB,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,qBAAqB,KAAK,WAAW,CAAC,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,EAAE;4BACxG,eAAe;4BACf,gGAAgG;4BAChG,EAAE;4BACF,wEAAwE;4BACxE,2EAA2E;4BAC3E,uDAAuD;4BACvD,EAAE;4BACF,wBAAwB;4BACxB,kFAAkF;4BAClF,EAAE;4BACF,MAAM,IAAI,SAAS,CACjB,cAAc,IAAI,0DAA0D,qBAAqB,CAAC,IAAI,WAAW,CAClH,CAAC;yBACH;6BAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,OAAO,EAAE;4BAChD,6BAA6B;4BAC7B,yFAAyF;4BACzF,2DAA2D;4BAC3D,uGAAuG;4BACvG,mCAAmC;4BACnC,wGAAwG;4BACxG,QAAQ;4BAER,uEAAuE;4BAEvE,8DAA8D;4BAC9D,IAAI,GAAI,qBAA6B,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;yBAC1D;6BAAM;4BACL,qDAAqD;4BACrD,8DAA8D;4BAC9D,IAAI,GAAI,qBAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;yBAClD;qBACF;yBAAM,IAAI,IAAI,YAAY,qBAAqB,EAAE;wBAChD,IAAI,GAAG,IAAI,CAAC;qBACb;yBAAM,IAAI,IAAI,YAAY,iBAAiB,EAAE;wBAC5C,IAAI,IAAI,KAAK,OAAO,EAAE;4BACpB,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;yBAC9B;6BAAM;4BACL,MAAM,IAAI,SAAS,CAAC,yDAAyD,CAAC,CAAC;yBAChF;qBACF;yBAAM,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,YAAY,WAAW,IAAI,qBAAqB,KAAK,WAAW,EAAE;wBACrG,kEAAkE;wBAClE,oGAAoG;wBACpG,uCAAuC;wBAEvC,8DAA8D;wBAC9D,IAAI,GAAG,IAAK,UAAkB,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;qBACxF;yBAAM;wBACL,MAAM,IAAI,SAAS,CAAC,KAAK,IAAI,kCAAkC,qBAAqB,EAAE,CAAC,CAAC;qBACzF;iBACF;aACF;iBAAM;gBACL,EAAE;gBACF,mCAAmC;gBACnC,EAAE;gBACF,SAAS,GAAG,IAAI,CAAC;gBACjB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACvB,2CAA2C;oBAC3C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACrB,MAAM,IAAI,SAAS,CAAC,qDAAqD,CAAC,CAAC;qBAC5E;oBACD,MAAM,gBAAgB,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;oBACxC,IAAI,gBAAgB,KAAK,QAAQ,EAAE;wBACjC,IAAI,GAAG,QAAQ,CAAC;wBAChB,IAAI,GAAG,IAAI,CAAC;qBACb;yBAAM,IAAI,gBAAgB,KAAK,SAAS,EAAE;wBACzC,IAAI,GAAG,MAAM,CAAC;wBACd,0GAA0G;wBAC1G,gDAAgD;wBAChD,8DAA8D;wBAC9D,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,IAAa,CAAC,CAAC;qBACvC;yBAAM;wBACL,MAAM,IAAI,SAAS,CAAC,uCAAuC,gBAAgB,GAAG,CAAC,CAAC;qBACjF;iBACF;qBAAM,IAAI,IAAI,YAAY,iBAAiB,EAAE;oBAC5C,IAAI,GAAG,OAAO,CAAC;oBACf,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC9B;qBAAM;oBACL,kCAAkC;oBAClC,MAAM,UAAU,GAAG,mEAAqC,CAAC,GAAG,CAC1D,IAAI,CAAC,WAA8C,CACpD,CAAC;oBACF,IAAI,UAAU,KAAK,SAAS,EAAE;wBAC5B,MAAM,IAAI,SAAS,CAAC,qCAAqC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;qBAC/E;oBACD,IAAI,GAAG,UAAU,CAAC;oBAClB,IAAI,GAAG,IAA2B,CAAC;iBACpC;aACF;YAED,kDAAkD;YAClD,IAAI,SAAS,KAAK,SAAS,EAAE;gBAC3B,oCAAoC;gBACpC,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC3B;iBAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACpC,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;aAC/D;YACD,IAAI,GAAG,SAA8B,CAAC;YAEtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;SAC3B;QAED,wBAAwB;QACxB,MAAM,IAAI,GAAG,IAAA,oCAAa,EAAC,IAAI,CAAC,CAAC;QACjC,mEAAmE;QACnE,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YAChD,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACxF,2GAA2G;aAC5G;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,gCAAgC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;aAC/F;SACF;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IACD,aAAa;IAEb,kBAAkB;IAClB,MAAM,CAAC,KAAK,CAAC,SAAS,CACpB,KAA0D,EAC1D,OAIwB;QAExB,OAAO,IAAA,wCAAe,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAED,MAAM,CAAC,WAAW,CAChB,OAA0B,EAC1B,OAAoC;QAEpC,OAAO,IAAA,0CAAiB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED,MAAM,CAAC,aAAa,CAClB,SAA8B,EAC9B,OAAsC;QAEtC,OAAO,IAAA,4CAAmB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAED,MAAM,CAAC,YAAY,CACjB,QAA4B,EAC5B,OAAqC;QAErC,OAAO,IAAA,2CAAkB,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,CAAC,gBAAgB,CACrB,IAAO,EACP,MAAsC,EACtC,IAAwB;QAExB,OAAO,IAAA,+CAAsB,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAED,aAAa;IAEb,sBAAsB;IACtB,SAAS,CAAC,OAAgC;QACxC,OAAO,IAAA,2CAAe,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,WAAW,CAAC,OAAkC;QAC5C,OAAO,IAAA,6CAAiB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAkDD,aAAa;IAEb,qBAAqB;IACrB,IAAI,IAAI;QACN,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,IAAI,KAAK,CACb,uEAAuE;gBACrE,2EAA2E,CAC9E,CAAC;SACH;QACD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,IAAI,OAAO;QACT,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,IAAI,SAAS;QACX,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED,IAAI,QAAQ;QACV,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAChE;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IACD,aAAa;IAEb,kBAAkB;IAElB,KAAK,CAAC,OAAO,CAAC,WAAqB;QACjC,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,QAAQ,IAAI,CAAC,YAAY,EAAE;YACzB,KAAK,KAAK,CAAC;YACX,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,IAAI,CAAC;YACnB,KAAK,SAAS,CAAC;YACf,KAAK,YAAY,CAAC;YAClB,KAAK,WAAW,CAAC,CAAC;gBAChB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBACpB,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;iBACxF;gBACD,IAAI,IAAI,CAAC,aAAa,EAAE;oBACtB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;iBAC5D;gBACD,IAAI;oBACF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;oBAC1B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;oBACrC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;oBAC5B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;oBAEpB,IAAI,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE;wBAChC,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAChB,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;qBAC3B;oBAED,OAAO,IAAI,CAAC;iBACb;wBAAS;oBACR,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;iBAC5B;aACF;YACD;gBACE,MAAM,IAAI,KAAK,CAAC,kCAAkC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;SAC1E;IACH,CAAC;IAED,OAAO;QACL,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC5D;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;SAC3B;QACD,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;QAChC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAE/B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;IAC7B,CAAC;IAED,aAAa;IAEb,2BAA2B;IACnB,WAAW;QACjB,IAAI,IAAI,CAAC,YAAY,KAAK,MAAM,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;IACH,CAAC;IAED,OAAO,CAAC,IAAuB;QAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACpE;QACD,OAAO,IAAA,oCAAa,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;CAEF;AAhgBD,wBAggBC"}