{"version": 3, "file": "tensor-impl-type-mapping.js", "sourceRoot": "", "sources": ["../../lib/tensor-impl-type-mapping.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,kCAAkC;AAkBlC,kGAAkG;AAClG,MAAM,CAAC,MAAM,qCAAqC,GAAG,IAAI,GAAG,CAA0C;IACpG,CAAC,SAAS,EAAE,YAAY,CAAC;IACzB,CAAC,OAAO,EAAE,UAAU,CAAC;IACrB,CAAC,MAAM,EAAE,SAAS,CAAC;IACnB,CAAC,QAAQ,EAAE,WAAW,CAAC;IACvB,CAAC,OAAO,EAAE,UAAU,CAAC;IACrB,CAAC,OAAO,EAAE,UAAU,CAAC;IACrB,CAAC,MAAM,EAAE,UAAU,CAAC;IACpB,CAAC,SAAS,EAAE,YAAY,CAAC;IACzB,CAAC,QAAQ,EAAE,WAAW,CAAC;IACvB,CAAC,MAAM,EAAE,UAAU,CAAC;IACpB,CAAC,OAAO,EAAE,UAAU,CAAC;CACtB,CAAC,CAAC;AAEH,kGAAkG;AAClG,MAAM,CAAC,MAAM,qCAAqC,GAAG,IAAI,GAAG,CAA+C;IACzG,CAAC,YAAY,EAAE,SAAS,CAAC;IACzB,CAAC,UAAU,EAAE,OAAO,CAAC;IACrB,CAAC,SAAS,EAAE,MAAM,CAAC;IACnB,CAAC,WAAW,EAAE,QAAQ,CAAC;IACvB,CAAC,UAAU,EAAE,OAAO,CAAC;IACrB,CAAC,UAAU,EAAE,OAAO,CAAC;IACrB,CAAC,YAAY,EAAE,SAAS,CAAC;IACzB,CAAC,WAAW,EAAE,QAAQ,CAAC;CACxB,CAAC,CAAC;AAEH,oHAAoH;AACpH,oHAAoH;AACpH,yBAAyB;AACzB,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAChC,MAAM,CAAC,MAAM,eAAe,GAAG,GAAG,EAAE;IAClC,IAAI,CAAC,mBAAmB,EAAE;QACxB,mBAAmB,GAAG,IAAI,CAAC;QAC3B,MAAM,wBAAwB,GAAG,OAAO,aAAa,KAAK,WAAW,IAAI,aAAa,CAAC,IAAI,CAAC;QAC5F,MAAM,yBAAyB,GAAG,OAAO,cAAc,KAAK,WAAW,IAAI,cAAc,CAAC,IAAI,CAAC;QAE/F,oGAAoG;QACpG,MAAM,YAAY,GAAI,UAAkB,CAAC,YAAY,CAAC;QACtD,MAAM,uBAAuB,GAAG,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,CAAC,IAAI,CAAC;QAEzF,IAAI,wBAAwB,EAAE;YAC5B,qCAAqC,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAClE,qCAAqC,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;SACnE;QACD,IAAI,yBAAyB,EAAE;YAC7B,qCAAqC,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YACpE,qCAAqC,CAAC,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;SACrE;QACD,IAAI,uBAAuB,EAAE;YAC3B,qCAAqC,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YACnE,qCAAqC,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;SACpE;aAAM;YACL,yEAAyE;YACzE,qCAAqC,CAAC,GAAG,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;SACnE;KACF;AACH,CAAC,CAAC"}