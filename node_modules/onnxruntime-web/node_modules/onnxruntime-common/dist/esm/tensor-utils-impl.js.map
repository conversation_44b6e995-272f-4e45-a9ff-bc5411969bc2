{"version": 3, "file": "tensor-utils-impl.js", "sourceRoot": "", "sources": ["../../lib/tensor-utils-impl.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,kCAAkC;AAQlC,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C;;;;GAIG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,IAAwB,EAAU,EAAE;IAChE,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;YACzD,MAAM,IAAI,SAAS,CAAC,QAAQ,CAAC,8BAA8B,GAAG,EAAE,CAAC,CAAC;SACnE;QACD,IAAI,GAAG,GAAG,CAAC,EAAE;YACX,MAAM,IAAI,UAAU,CAAC,QAAQ,CAAC,0CAA0C,GAAG,EAAE,CAAC,CAAC;SAChF;QACD,IAAI,IAAI,GAAG,CAAC;KACb;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,MAAc,EAAE,IAAuB,EAAU,EAAE;IAC/E,QAAQ,MAAM,CAAC,QAAQ,EAAE;QACvB,KAAK,KAAK;YACR,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACpD,KAAK,YAAY;YACf,OAAO,IAAI,MAAM,CAAC;gBAChB,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,MAAM,CAAC,IAA8C;gBAC3D,IAAI,EAAE,MAAM,CAAC,IAA8C;gBAC3D,IAAI;aACL,CAAC,CAAC;QACL,KAAK,SAAS;YACZ,OAAO,IAAI,MAAM,CAAC;gBAChB,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,IAAI,EAAE,MAAM,CAAC,IAA4C;gBACzD,IAAI;aACL,CAAC,CAAC;QACL,KAAK,YAAY;YACf,OAAO,IAAI,MAAM,CAAC;gBAChB,QAAQ,EAAE,YAAY;gBACtB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,IAAI,EAAE,MAAM,CAAC,IAA8C;gBAC3D,IAAI;aACL,CAAC,CAAC;QACL,KAAK,WAAW;YACd,OAAO,IAAI,MAAM,CAAC;gBAChB,QAAQ,EAAE,WAAW;gBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,IAAI,EAAE,MAAM,CAAC,IAA6C;gBAC1D,IAAI;aACL,CAAC,CAAC;QACL;YACE,MAAM,IAAI,KAAK,CAAC,kCAAkC,MAAM,CAAC,QAAQ,mBAAmB,CAAC,CAAC;KACzF;AACH,CAAC,CAAC"}