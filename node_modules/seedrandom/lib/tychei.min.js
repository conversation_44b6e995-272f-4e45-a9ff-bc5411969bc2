!function(t,n,e){function u(t){var r=this,n="";r.next=function(){var t=r.b,n=r.c,e=r.d,o=r.a;return t=t<<25^t>>>7^n,n=n-e|0,e=e<<24^e>>>8^o,o=o-t|0,r.b=t=t<<20^t>>>12^n,r.c=n=n-e|0,r.d=e<<16^n>>>16^o,r.a=o-t|0},r.a=0,r.b=0,r.c=-1640531527,r.d=1367130551,t===Math.floor(t)?(r.a=t/4294967296|0,r.b=0|t):n+=t;for(var e=0;e<n.length+20;e++)r.b^=0|n.charCodeAt(e),r.next()}function c(t,n){return n.a=t.a,n.b=t.b,n.c=t.c,n.d=t.d,n}function o(t,n){function e(){return(o.next()>>>0)/4294967296}var o=new u(t),r=n&&n.state;return e.double=function(){do{var t=((o.next()>>>11)+(o.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},e.int32=o.next,e.quick=e,r&&("object"==typeof r&&c(r,o),e.state=function(){return c(o,{})}),e}n&&n.exports?n.exports=o:e&&e.amd?e(function(){return o}):this.tychei=o}(0,"object"==typeof module&&module,"function"==typeof define&&define);