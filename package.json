{"name": "banana-bun", "version": "1.0.0", "description": "A bun-sized AI assistant for organizing your digital life with local AI models", "module": "src/index.ts", "type": "module", "private": false, "keywords": ["ai", "media", "organization", "bun", "privacy", "local-ai", "transcription", "search"], "author": "Your Name", "license": "MIT", "scripts": {"start": "bun run src/index.ts", "dev": "bun --watch src/index.ts", "test": "bun test", "test:watch": "bun test --watch", "test:report": "bun test --coverage --coverage-reporter=lcov --preload ./test-setup.ts | tee test-results.txt", "test:mcp": "bun run test-mcp-integration.ts", "mcp:chromadb": "bun run src/mcp/chromadb-server.ts", "mcp:monitor": "bun run src/mcp/monitor-server.ts", "mcp:meilisearch": "bun run src/mcp/meilisearch-server.ts", "mcp:whisper": "bun run src/mcp/whisper-server.ts", "mcp:intelligence": "bun run src/mcp/media-intelligence-server.ts", "lint-task": "bun run src/cli/lint-task.ts", "migrate": "bun run src/migrations/migrate-all.ts", "schedule": "bun run src/cli/schedule-manager.ts", "media-ingest": "bun run src/cli/media-ingest.ts", "media-organize": "bun run src/cli/organize-media.ts", "media-search": "bun run src/cli/media-search.ts", "smart-search": "bun run src/cli/smart-media-search.ts", "smart-transcribe": "bun run src/cli/smart-transcribe.ts", "media-intelligence": "bun run src/cli/media-intelligence.ts", "media-tags": "bun run src/cli/media-tags.ts", "banana-summarize": "bun run src/cli/banana-summarize.ts", "banana-recommend": "bun run src/cli/banana-recommend.ts", "banana-detect-scenes": "bun run src/cli/banana-detect-scenes.ts", "banana-detect-objects": "bun run src/cli/banana-detect-objects.ts", "banana-audio-analyze": "bun run src/cli/banana-audio-analyze.ts", "banana-embed-media": "bun run src/cli/banana-embed-media.ts", "banana-search-similar": "bun run src/cli/banana-search-similar.ts", "analyze-task-metrics": "bun run src/cli/analyze-task-metrics.ts", "run-feedback-loop": "bun run src/cli/run-feedback-loop.ts"}, "devDependencies": {"@types/bun": "latest", "@types/ws": "^8.5.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@chroma-core/default-embed": "^0.1.8", "@modelcontextprotocol/sdk": "^0.5.0", "@tensorflow/tfjs-node": "^4.22.0", "@types/node": "24.0.1", "better-sqlite3": "^11.10.0", "chromadb": "^3.0.1", "meilisearch": "0.51.0", "music-metadata": "^11.2.3", "node-ffmpeg-stream": "^1.1.0", "openai": "^5.3.0", "ws": "^8.18.0", "yaml": "^2.8.0"}}